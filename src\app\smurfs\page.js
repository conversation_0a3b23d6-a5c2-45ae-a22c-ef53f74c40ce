"use client";
import StoreIcons from "@/components/Footer/StoreIcons";
import { useTranslations } from "next-intl";
import Head from "next/head";
import Image from "next/image";
import styles from "./styles.module.css";

const SmurfsComingSoon = () => {
  const t = useTranslations("SmurfsPage");

  // Commented out for future use when videos are implemented
  // const mainTrailerVideoId = "dQw4w9WgXcQ"; // Replace with actual trailer video ID
  // const teaserVideos = [
  //   { id: 1, title: "Smurfs Learning Adventure", videoId: "dQw4w9WgXcQ" },
  //   { id: 2, title: "Educational Games Preview", videoId: "dQw4w9WgXcQ" },
  //   { id: 3, title: "Character Introduction", videoId: "dQw4w9WgXcQ" },
  //   { id: 4, title: "Gameplay Highlights", videoId: "dQw4w9WgXcQ" },
  // ];

  // Game cards for display - separate from videos
  const gameCards = [
    { id: 1, text: t("gameCards.card1") },
    { id: 2, text: t("gameCards.card2") },
    { id: 3, text: t("gameCards.card3") },
    { id: 4, text: t("gameCards.card4") },
    { id: 5, text: t("gameCards.card5") },
    { id: 6, text: t("gameCards.card6") },
    { id: 7, text: t("gameCards.card7") },
    { id: 8, text: t("gameCards.card8") },
  ];

  // Commented out for future use when store icons are implemented
  const icons = [
    // {
    //   src: "/images/footer/socialIcons/Amazon.webp",
    //   alt: t("Amazon"),
    //   onClick: () => window.open("https://www.amazon.com/gp/product/B0DH6RT2JV", "_blank"),
    // },
    {
      src: "/images/footer/socialIcons/pre-order-app-store.webp",
      alt: t("Appstore"),
      onClick: () =>
        window.open(
          "https://apps.apple.com/us/app/smurfs-playhouse-by-skidos/id6746158668",
          "_blank"
        ),
    },
    // {
    //   src: "/images/footer/socialIcons/Playstore.webp",
    //   alt: t("Playstore"),
    //   onClick: () =>
    //     window.open(
    //       "https://play.google.com/store/apps/details?id=skidos.shopping.toddler.learning.games&hl=en_IN",
    //       "_blank"
    //     ),
    // },
  ];

  return (
    <>
      <Head>
        <title>{t("metadata.title")}</title>
        <meta name="description" content={t("metadata.description")} />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </Head>

      <div className={styles.smurfsPageWrapper}>
        {/* First Frame - Hero Banner */}
        <div className={styles.firstFrame}>
          {/* Desktop Banner */}
          <div className={styles.desktopBanner}></div>

          {/* Mobile Banner */}
          <div className={styles.mobileBanner}></div>
        </div>

        {/* Second Frame - White background with text */}
        <div className={styles.secondFrame}>
          <div className={styles.secondFrameContent}>
            <h1 className={styles.mainHeading}>{t("mainHeading")}</h1>
            <p className={styles.subHeading}>{t("subHeading")}</p>
            <div style={{ width: "100%" }}>
              <StoreIcons icons={icons} styleClass={styles.customStoreIcons} />
            </div>
            {/* <p className={styles.subHeading}>Download now and play!</p> */}
            {/* <StoreIcons icons={icons} styleClass={styles.storeIconsWrapper} /> */}
            <p className={styles.description}>{t("description")}</p>
          </div>
          {/* <h1 className={styles.teaserTrailerHeading}>Watch the trailer now</h1>
          <div className={styles.trailerVideoContainer}>
            <YouTubeVideo
              videoId={mainTrailerVideoId}
              title="SKIDOS x Smurfs Official Trailer"
              className={styles.trailerVideo}
            />
          </div>

          <h1 className={styles.teaserTrailerHeading}>Teaser</h1>
          <div className={styles.teaserVideosContainer}>
            {teaserVideos.map((video) => (
              <div key={video.id} className={styles.teaserVideoCard}>
                <YouTubeVideo
                  videoId={video.videoId}
                  title={video.title}
                  className={styles.teaserVideo}
                />
              </div>
            ))}
          </div> */}

          {/* <p className={styles.description}>
            Get ready for a magical adventure in learning! SKIDOS and the Smurfs have teamed up to
            bring your kids exciting educational games with their favorite blue buddies. Fun meets
            learning like never before!
          </p> */}
          {/* <FormCtaButton text="Notify Me" /> */}
        </div>

        {/* Third Frame - Game screenshots */}
        <div className={styles.thirdFrame}>
          <div className={styles.thirdFrameContent}>
            <div className={styles.gamesContainer}>
              {gameCards.map((card) => (
                <div key={card.id} className={styles.gameCard}>
                  <Image
                    src={`/images/skidos-smurfs/smurf${card.id}.webp`}
                    width={560}
                    height={420}
                    alt={t("gameScreenshotAlt")}
                    sizes="(max-width: 480px) 250px, (max-width: 768px) 320px, (max-width: 1024px) 400px, (max-width: 1440px) 480px, 560px"
                  />
                </div>
              ))}
            </div>
          </div>

          {/* <div style={{ paddingLeft: "1.5rem" }}>
            <h1 className={styles.teaserTrailerHeading}>header 1</h1>
            <p className={styles.description}>
              Get ready for a magical adventure in learning! SKIDOS and the Smurfs have teamed up to
              bring your kids exciting educational games with their favorite blue buddies. Fun meets
              learning like never before!
            </p>

            <h1 className={styles.teaserTrailerHeading}>header 2</h1>
            <p className={styles.description}>
              <ul>
                <li>Get ready for a magical adventure in learning! SKIDOS and</li>
                <li>Get ready for a magical adventure in learning! SKIDOS and</li>
                <li>Get ready for a magical adventure in learning! SKIDOS and</li>
              </ul>
            </p>
          </div> */}
        </div>
      </div>
    </>
  );
};

export default SmurfsComingSoon;
