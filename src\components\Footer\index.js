"use client";

import AwardReviewSection from "@/app/home/<USER>";
import { useAuth } from "@/context/AuthContext";
import useIsMobile from "@/hooks/useIsMobile";
import useScrollAnimation from "@/hooks/useScrollAnimation";
import { trackWebEngageEvent } from "@/utils/webengage";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import StoreIcons from "./StoreIcons";
import styles from "./styles.module.css";

gsap.registerPlugin(ScrollTrigger);

const Footer = () => {
  const isMobile = useIsMobile();
  const scrollRef = useRef(null);
  const lockContentRef = useRef(null);
  const lockIconRef = useRef(null);
  const [isUnlocked, setIsUnlocked] = useState(false);
  const router = useRouter();
  const { isLoggedIn, isSubscribed } = useAuth();
  const pathname = usePathname();

  const t = useTranslations("Footer");
  const tHomePage = useTranslations("HomePage");

  const icons = [
    {
      src: "/images/footer/socialIcons/Amazon.webp",
      alt: t("Amazon"),
      onClick: () => window.open("https://www.amazon.com/gp/product/B0DH6RT2JV", "_blank"),
    },
    {
      src: "/images/footer/socialIcons/Appstore.webp",
      alt: t("Appstore"),
      onClick: () =>
        window.open(
          "https://apps.apple.com/us/app/skidos-learning-games-for-kids/id1483744837",
          "_blank"
        ),
    },
    {
      src: "/images/footer/socialIcons/Playstore.webp",
      alt: t("Playstore"),
      onClick: () =>
        window.open(
          "https://play.google.com/store/apps/details?id=skidos.shopping.toddler.learning.games&hl=en_IN",
          "_blank"
        ),
    },
    {
      src: "/images/footer/socialIcons/Web.webp",
      alt: t("Web"),
      onClick: () => handleWebIcon(),
    },
  ];

  const scrollToTop = (path) => {
    if (window.location.pathname === path) {
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  const handleRedirect = () => {
    router.push("/acquisition");
  };

  const specificPages = ["/user-home-screen/", "/game-player/"];
  const isSpecificPage = specificPages.includes(pathname);

  const handleWebIcon = () => {
    trackWebEngageEvent("WebGLFooterIconClk");

    if (isSubscribed) {
      router.push("/user-home-screen");
    } else {
      router.push("/get-started");
    }
  };

  useEffect(() => {
    gsap.set(scrollRef.current, { y: 100, opacity: 0 });
    gsap.set(lockContentRef.current, { y: 50 });
    ScrollTrigger.create({
      trigger: lockContentRef.current,
      start: "top center",
      onEnter: () => {
        gsap.to(lockContentRef.current, { y: -50, duration: 0.5 });
        gsap.to(scrollRef.current, {
          y: 0,
          opacity: 1,
          duration: 2,
          delay: 0.5,
        });
        gsap.to(lockIconRef.current, {
          duration: 0.5,
          onComplete: () => {
            setIsUnlocked(true);
            gsap.to(lockIconRef.current, { opacity: 1, duration: 0.5 });
          },
        });
      },
    });
  }, []);

  const setRef = useScrollAnimation(
    {
      duration: 1,
      ease: "power3.out",
      start: "top bottom",
    },
    []
  );

  return (
    <>
      <div
        className={`${styles.footerWrapper} ${isSpecificPage ? styles.specificPageFooter : ""}`}
        id="footer"
      >
        <AwardReviewSection />
        {!isLoggedIn || (isLoggedIn && !isSubscribed) ? (
          <div className={styles.pinkFooterWrapper} id="pinkFooterContainer">
            <div ref={lockContentRef} className={styles.lockContentWrapper}>
              <p ref={lockIconRef}>
                {isUnlocked ? (
                  <Image src="/images/unlock.png" alt="Unlock Icon" width={96} height={96} />
                ) : (
                  <Image src="/images/lock.png" alt="Lock Icon" width={96} height={96} />
                )}
              </p>
              <p>{t("UnlockGrowth")}</p>
              <button className={styles.carouselBannerBtn} onClick={handleRedirect}>
                {t("UnlockNow")}
              </button>
            </div>
            <div ref={scrollRef} className={styles.gameCardsWrapper}>
              <div className={styles.gameCardsFirstSection}>
                <div className={styles.gameCardCol1}>
                  <div>
                    <Image
                      src="/images/pinkGameCard1.webp"
                      width={isMobile ? 166 : 250}
                      height={isMobile ? 271 : 379}
                      className={styles.gameCardCol1Img}
                      objectFit="contain"
                      alt={t("LearnToRead")}
                      placeholder="blur"
                      blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNctmhZPQAGUwJv7ObBEQAAAABJRU5ErkJggg=="
                    />
                    <p>{t("LearnToRead")}</p>
                  </div>
                </div>
                <div className={styles.gameCardCol2}>
                  <div>
                    <Image
                      src="/images/pinkGameCard2.webp"
                      width={isMobile ? 166 : 250}
                      height={isMobile ? 72 : 100}
                      className={styles.gameCardCol1Img}
                      objectFit="cover"
                      alt={t("Tracing")}
                    />
                    <p>{t("Tracing")}</p>
                  </div>
                  <div>
                    <Image
                      src="/images/pinkGameCard3.webp"
                      width={isMobile ? 166 : 250}
                      height={isMobile ? 165 : 238}
                      className={styles.gameCardCol1Img}
                      alt={t("Fitness")}
                    />
                    <p>{t("Fitness")}</p>
                  </div>
                </div>
              </div>
              <div className={styles.gameCardsFirstSection}>
                <div className={styles.gameCardCol2}>
                  <div>
                    <Image
                      src="/images/pinkGameCard7.webp"
                      width={isMobile ? 166 : 250}
                      height={isMobile ? 122 : 174}
                      className={styles.gameCardCol1Img}
                      objectFit="cover"
                      alt={t("Activities")}
                    />
                    <p>{t("Activities")}</p>
                  </div>
                  <div>
                    <Image
                      src="/images/pinkGameCard5.webp"
                      width={isMobile ? 166 : 250}
                      height={isMobile ? 104 : 158}
                      className={styles.gameCardCol1Img}
                      alt={t("EmotionalWellbeing")}
                    />
                    <p>{t("EmotionalWellbeing")}</p>
                  </div>
                </div>
                <div className={styles.gameCardCol2}>
                  <div>
                    <Image
                      src="/images/pinkGameCard6.webp"
                      width={isMobile ? 166 : 250}
                      height={isMobile ? 72 : 98}
                      className={styles.gameCardCol1Img}
                      alt={t("Math")}
                    />
                    <p>{t("Math")}</p>
                  </div>
                  <div>
                    <Image
                      src="/images/pinkGameCard4.webp"
                      width={isMobile ? 166 : 250}
                      height={isMobile ? 165 : 238}
                      className={styles.gameCardCol1Img}
                      alt={t("Avatars")}
                    />
                    <p>{t("Avatars")}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : null}
        <div className={styles.footerGreenWrapper}>
          <div className={styles.footerIconsDiv}>
            <StoreIcons icons={icons} />
          </div>
          <div className={styles.footerContentWrapper}>
            <div className={styles.footerMainContent}>
              <div className={styles.footerContentLeft}>
                <div>
                  <Link href="/" className="noLinkStyle">
                    <Image src="/images/skidosLogo.png" width={167} height={52} alt="SKIDOS Logo" />
                  </Link>
                </div>
                <div className={styles.footerContentLeftImg}>
                  <Link
                    href="https://www.instagram.com/skidoslearning/"
                    className="noLinkStyle"
                    prefetch={false}
                  >
                    <Image
                      src="/images/footer/socialIcons/Insta.png"
                      width={40}
                      height={40}
                      alt="Instagram"
                    />
                  </Link>
                  <Link
                    href="https://www.tiktok.com/@skidoslearning"
                    className="noLinkStyle"
                    prefetch={false}
                  >
                    <Image
                      src="/images/footer/socialIcons/TiktokIcon.png"
                      width={40}
                      height={40}
                      alt="TikTok"
                    />
                  </Link>
                  <Link
                    href="https://www.youtube.com/user/playatskidos"
                    className="noLinkStyle"
                    prefetch={false}
                  >
                    <Image
                      src="/images/footer/socialIcons/Youtube.png"
                      width={40}
                      height={40}
                      alt="Youtube"
                    />
                  </Link>
                  <Link
                    href="https://dk.linkedin.com/company/skidos-games"
                    className="noLinkStyle"
                    prefetch={false}
                  >
                    <Image
                      src="/images/footer/socialIcons/Linkedin.png"
                      width={40}
                      height={40}
                      alt="Linkedin"
                    />
                  </Link>
                </div>
              </div>
              <div className={styles.footerContentRight}>
                <div className={styles.footerContentRightTop}>
                  <div>
                    <h2>{t("AboutUs")}</h2>
                    <ul>
                      <Link
                        href="/blogs"
                        className="noLinkStyle"
                        prefetch={false}
                        onClick={() => scrollToTop("/blogs/")}
                      >
                        <li className={styles.linkHoverable}>{t("Blogs")}</li>
                      </Link>
                      <Link
                        href="/news"
                        className="noLinkStyle"
                        prefetch={false}
                        onClick={() => scrollToTop("/news/")}
                      >
                        <li className={styles.linkHoverable}>{t("News")}</li>
                      </Link>
                      <Link
                        href="/career"
                        className="noLinkStyle"
                        prefetch={false}
                        onClick={() => scrollToTop("/career/")}
                      >
                        <li className={styles.linkHoverable}>{t("Careers")}</li>
                      </Link>
                    </ul>
                  </div>
                  <div>
                    <h2>{t("Support")}</h2>
                    <ul>
                      <Link
                        href="/terms"
                        className="noLinkStyle"
                        prefetch={false}
                        onClick={() => scrollToTop("/terms/")}
                      >
                        <li className={styles.linkHoverable}>{t("TermsAndConditions")}</li>
                      </Link>
                      <Link
                        href="/privacy-policy"
                        className="noLinkStyle"
                        prefetch={false}
                        onClick={() => scrollToTop("/privacy-policy/")}
                      >
                        <li className={styles.linkHoverable}>{t("PrivacyPolicy")}</li>
                      </Link>
                      <Link
                        href="https://support.skidos.com/support/home"
                        className="noLinkStyle"
                        prefetch={false}
                      >
                        <li className={styles.linkHoverable}>{t("FAQ")}</li>
                      </Link>
                      <Link
                        href="/partnership"
                        className="noLinkStyle"
                        prefetch={false}
                        onClick={() => scrollToTop("/partnership/")}
                      >
                        <li className={styles.linkHoverable}>{t("Partnership")}</li>
                      </Link>
                    </ul>
                  </div>
                  <div>
                    <h2>{t("Contact")}</h2>
                    <ul>
                      <li>
                        Skidos Labs ApS,
                        <br />
                        Titangade 11
                        <br />
                        2200 København N<br />
                        CVR: 37212962
                      </li>
                      <li>
                        <a
                          className="noLinkStyle"
                          href="mailto:<EMAIL>"
                          target="_blank"
                          rel="noopener noreferrer"
                          data-stringify-link="mailto:<EMAIL>"
                          data-sk="tooltip_parent"
                          aria-haspopup="menu"
                          aria-expanded="false"
                        >
                          <EMAIL>
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div className={styles.footerContentRightBottom}></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Footer;
