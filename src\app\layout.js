import AnalyticsScripts from "@/components/AnalyticsScripts/AnalyticsScripts";
import Footer from "@/components/Footer";
import NavbarHome from "@/components/Navbar";
import ReferralFactory from "@/components/ReferralFactory";
import { AuthProvider } from "@/context/AuthContext";
import ProtectedRoute from "@/utils/ProtectedRoute";
import ScrollToTop from "@/utils/ScrollToTop";
import { Analytics } from "@vercel/analytics/next";
import { SpeedInsights } from "@vercel/speed-insights/next";
import { NextIntlClientProvider } from "next-intl";
import { getLocale, getMessages } from "next-intl/server";
import { Poppins } from "next/font/google";
import localFont from "next/font/local";
import "./globals.css";

const NeverMindBold = localFont({
  src: "../assets/fonts/NeverMindDisplay-Bold-BF6501204d66b5c.ttf",
  weight: "700",
  style: "normal",
  variable: "--font-nevermind-bold",
});

const NeverMindMedium = localFont({
  src: "../assets/fonts/NeverMindDisplay-Medium-BF6501205310ee9.ttf",
  weight: "600",
  style: "normal",
  variable: "--font-nevermind-medium",
});

const NeverMindLight = localFont({
  src: "../assets/fonts/NeverMindDisplay-Light-BF6501205364152.ttf",
  weight: "400",
  style: "normal",
  variable: "--font-nevermind-light",
});

const PoppinsFont = Poppins({
  weight: ["400", "500", "600", "700"],
  style: "normal",
  subsets: ["latin"],
  variable: "--font-poppins",
});

export default async function RootLayout({ children }) {
  const locale = await getLocale();
  const messages = await getMessages();
  return (
    <html lang="en">
      <head>
        <AnalyticsScripts />
      </head>
      <body
        className={`${NeverMindBold.variable} ${NeverMindLight.variable} ${PoppinsFont.variable} ${NeverMindMedium.variable}`}
      >
        {process.env.NEXT_PUBLIC_REFERRAL_FACTORY && <ReferralFactory />}
        <NextIntlClientProvider messages={messages}>
          <AuthProvider>
            <ProtectedRoute>
              <NavbarHome />
              <ScrollToTop />
              <main>{children}</main>
              <Footer />
            </ProtectedRoute>
          </AuthProvider>
        </NextIntlClientProvider>
        {/* vercel Analytics and SpeedInsights */}
        <Analytics />
        <SpeedInsights />
      </body>
    </html>
  );
}
