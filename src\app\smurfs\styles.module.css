
/* ===== GLOBAL STYLES ===== */

/* Page Wrapper - Matches home page approach */
.smurfsPageWrapper {
  max-width: 1728px;
  margin: 0 auto;
  /* background-color: #f7f0ea; */
}

/* FIRST FRAME - Hero Banner */
.firstFrame {
  width: 100%;
  position: relative;
  overflow: hidden;
  /* background-color: #f7f0ea; */
}

.customStoreIcons{
  width: 100%;
  display: block;
}

/* Desktop Banner */
.desktopBanner {
  display: block;
  width: 100%;
  height: 720px;
  background-image: url("/images/skidos-smurfs/banner-smurf-skidos.webp");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

/* Mobile Banner */
.mobileBanner {
  display: none;
  width: 100%;
  height: 500px;
  background-image: url("/images/skidos-smurfs/banner-smurf-skidos-mobile.webp");
  background-size: contain;
  /* background-position: center; */
  background-repeat: no-repeat;
}

/* SECOND FRAME - Content Section */
.secondFrame {
  background: #FFFFFF;
  padding: clamp(1rem, 6vw, 3rem) 0;
  width: 100%;
}

.secondFrameContent {
  padding: 0 clamp(2rem, 8vw, 8rem);
  /* padding: 0 !important; */
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 100%;
}

/* Typography */
.mainHeading {
  font-size: clamp(2rem, 6vw, 4rem);
  font-weight: 700;
  font-family: var(--font-poppins);
  color: #EA73C0;
  margin: 0 0 1rem 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  text-align: center;
  line-height: 1.2;
}

.subHeading {
  font-size: clamp(1rem, 3vw, 1.8rem);
  font-family: var(--font-poppins);
  margin: 0 0 1.5rem 0;
  color: #949494;
  text-align: center;
  font-weight: 500;
}

.description {
  font-size: clamp(1rem, 2.5vw, 1.4rem);
  color: #86868B;
  font-family: var(--font-poppins);
  line-height: 1.8;
  margin: 1rem 0 2rem 0;
  width: 100%;
  text-align: center;
  font-weight: 600;
}

/* THIRD FRAME - Game Screenshots */
.thirdFrame {
  padding: 0 0 clamp(2rem, 8vw, 5rem) 0;
  width: 100%;
}

.thirdFrameContent {
  padding: 0 clamp(2rem, 8vw, 8rem);
  /* padding: 0 !important; */
  box-sizing: border-box;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
  max-width: 100%;
}

.thirdFrameContent::-webkit-scrollbar {
  display: none;
}

.gamesContainer {
  display: flex;
  gap: clamp(0.75rem, 3vw, 1.25rem);
  padding: 0;
  width: max-content;
  min-width: 100%;
}

.gameCard {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  width: clamp(280px, 50vw, 560px);
  height: clamp(210px, 37.5vw, 420px);
  /* transition: transform 0.3s ease; */
  /* flex-shrink: 0; */
  min-width: 280px;
}

/* .gameCard:hover {
  transform: translateY(-5px);
} */

.gameCard img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
/* ===== RESPONSIVE STYLES ===== */

/* ===== RESPONSIVE BREAKPOINTS ===== */

/* Large Desktop (1440px+) */
@media only screen and (min-width: 1440px) {
  .secondFrameContent,
  .thirdFrameContent {
    padding: 0 3rem;
  }

  .desktopBanner {
    height: clamp(500px, 50vw, 875px);
  }
}

/* Desktop (1024px - 1439px) */
@media only screen and (min-width: 1024px) and (max-width: 1439px) {
  .secondFrameContent,
  .thirdFrameContent {
    padding: 0 7rem;
  }

  .desktopBanner {
    height: clamp(400px, 50vw, 600px);
  }
}

/* Tablet (768px - 1023px) */
@media only screen and (min-width: 769px) and (max-width: 1023px) {
  .secondFrameContent,
  .thirdFrameContent {
    padding: 0 3rem;
  }

  .desktopBanner {
    height: clamp(350px, 45vw, 500px);
  }

  .gameCard {
    flex: 0 0 280px;
  }
}

/* Mobile (up to 768px) */
@media only screen and (max-width: 768px) {
  .desktopBanner {
    display: none;
  }

  .mobileBanner {
    display: block;
    background-position: center;
  }

  .secondFrameContent,
  .thirdFrameContent {
    padding: 0 2rem;
    align-items: flex-start;
    text-align: left;
  }

  .mainHeading,
  .subHeading,
  .description {
    text-align: left;
  }

  .gameCard {
    flex: 0 0 250px;
  }
}

/* Small Mobile (up to 480px) */
@media only screen and (max-width: 480px) {
  .secondFrameContent,
  .thirdFrameContent {
    padding: 0 1.5rem;
  }

  .gameCard {
    flex: 0 0 220px;
  }

  .gamesContainer {
    gap: 0.75rem;
  }
}

/* Extra Small Mobile (up to 360px) */
@media only screen and (max-width: 360px) {
  .secondFrameContent,
  .thirdFrameContent {
    padding: 0 1rem;
  }

  .gameCard {
    flex: 0 0 200px;
  }

  .gamesContainer {
    gap: 0.5rem;
  }

  .mainHeading {
    font-size: clamp(1.5rem, 5vw, 2.5rem);
  }
}
