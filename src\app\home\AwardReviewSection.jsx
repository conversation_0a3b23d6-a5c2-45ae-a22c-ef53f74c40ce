import { LandingReview } from "@/constants";
import useScrollAnimation from "@/hooks/useScrollAnimation";
import { useTranslations } from "next-intl";
import Image from "next/image";
import styles from "./styles.module.css";

const AwardReviewSection = () => {
  const tHomePage = useTranslations("HomePage");

  const setRef = useScrollAnimation(
    {
      duration: 1,
      ease: "power3.out",
      start: "top bottom",
    },
    []
  );
  return (
    <div className={styles.discoverPurpleBgWrapper}>
      <h2 className={`${styles.homeSubheaders} ${styles.whiteText}`} ref={(el) => setRef(el)}>
        {tHomePage("reviews.title")}
      </h2>
      <div className={styles.reviewWrapper}>
        {LandingReview.map((item, index) => (
          <div key={index} className={styles.reviewCard} ref={(el) => setRef(el)}>
            <div>
              <Image src={item.starImg} width={152} height={24} alt="Rating Stars" />
              <p>{item.content}</p>
            </div>
            <div className={styles.reviewCardFooter}>
              <p>{item.description}</p>
            </div>
          </div>
        ))}
      </div>

      <div className={styles.socialProofWrapper}>
        <div className={styles.socialProofFirstRowWrapper} ref={(el) => setRef(el)}>
          <div className={styles.socialProofText}>{tHomePage("socialProof.impactMade")}</div>
          <div className={styles.socialProofSkills}>
            <p>{tHomePage("socialProof.problemsSolved")}</p>
            <div className={styles.imageWrapper}>
              <Image
                alt="Social Proof Learners"
                src="/images/socialProofSkiils.webp"
                layout="fill"
                objectFit="contain"
              />
            </div>
          </div>
        </div>
        <div className={styles.socialProofSecondRowWrapper}>
          <div className={styles.happyLearnersWrapper}>
            <div className={styles.imageWrapper}>
              <Image
                alt="Social Proof Learners"
                src="/images/socialProofLearners.webp"
                layout="fill"
                objectFit="contain"
              />
            </div>
            <div>
              <p>
                {tHomePage("socialProof.happyLearners.title")}
                <br />
                {tHomePage("socialProof.happyLearners.subtitle")}
              </p>
            </div>
          </div>
          <div className={styles.happyLearnersWrapper}>
            <div className={styles.imageWrapper}>
              <Image
                alt="Social Proof Rating"
                src="/images/socialProofRating.png"
                layout="fill"
                objectFit="contain"
              />
            </div>
            <div>
              <p>
                {tHomePage("socialProof.rating.title")}
                <br />
                <Image
                  src="/images/4.5_star_white.png"
                  width={100}
                  height={20}
                  alt="Rating Stars"
                />
              </p>
            </div>
          </div>
        </div>
      </div>
      <div className={styles.awardsWrapper}>
        <div ref={(el) => setRef(el)}>
          <h2>{tHomePage("awards.title")}</h2>
        </div>
        <div className={styles.awardImgWrapper}>
          <Image
            src="/images/Award1.webp"
            height={120}
            width={120}
            alt="Award"
            className={styles.awardImg}
            ref={(el) => setRef(el)}
          />
          <Image
            src="/images/Award2.webp"
            height={120}
            width={120}
            alt="Award"
            className={styles.awardImg}
            ref={(el) => setRef(el)}
          />
          <Image
            src="/images/Award3.webp"
            height={120}
            width={120}
            alt="Award"
            className={styles.awardImg}
            ref={(el) => setRef(el)}
          />
          <Image
            src="/images/Award4.webp"
            height={120}
            width={120}
            alt="Award"
            className={styles.awardImg}
            ref={(el) => setRef(el)}
          />
          <Image
            src="/images/Award5.webp"
            height={120}
            width={120}
            alt="Award"
            className={styles.awardImg}
            ref={(el) => setRef(el)}
          />
          <Image
            src="/images/Award6.webp"
            height={120}
            width={120}
            alt="Award"
            className={styles.awardImg}
            ref={(el) => setRef(el)}
          />
          <Image
            src="/images/Award7.png"
            height={120}
            width={120}
            alt="Award"
            className={styles.awardImg}
            ref={(el) => setRef(el)}
          />
        </div>
      </div>
    </div>
  );
};

export default AwardReviewSection;
