.footerWrapper {
  /* max-width: 1512px; */
  max-width: 1728px;
  margin: 0 auto;
}
.pinkFooterWrapper {
  background-image: url("/images/pinkBg.webp");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  width: 100%;
  padding: 8.3rem 0 1.5rem 0;
}

.lockContentWrapper {
  display: flex;
  align-items: center;
  flex-direction: column;
  font-size: 2.5rem;
}

.lockContentWrapper p:nth-child(1) {
  margin: 0;
}

.lockContentWrapper p:nth-child(2) {
  color: #ffff;
  margin: 1rem 0 2rem 0;
}

.carouselBannerBtn {
  margin-top: 0rem;
  background-color: #9258fe;
  font-size: 1.5rem;
  color: #ffff;
  border-radius: 1rem;
  padding: 0.8rem 2rem;
  text-decoration: none;
  border: none;
  box-shadow: 0px 10px 0px rgba(74, 45, 128, 1);
  cursor: pointer;
  font-family: var(--font-poppins);
  font-weight: 500;
}

.gameCardsWrapper {
  display: flex;
  gap: 10px;
  justify-content: center;
  text-align: center;
  margin: 2rem 2rem 0 2rem;
}
.gameCardsFirstSection {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}
.gameCardsFirstSection:nth-child(1) {
  justify-content: flex-end;
  gap: 20px;
}
.gameCardsFirstSection:nth-child(2) {
  justify-content: flex-start;
  gap: 20px;
}
.gameCardCol1 div {
  background-color: #ffff;
  padding: 5px;
  border-radius: 20px;
}
.gameCardCol1 p {
  margin: 0;
  font-size: 0.8rem;
}

.gameCardCol2 {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 15px;
}
.gameCardCol2 div {
  background-color: #ffff;
  padding: 5px;
  border-radius: 20px;
}
.gameCardCol2 p {
  margin: 0;
  font-size: 0.8rem;
}
.gameCardCol1Img {
  object-fit: fill;
}
.footerGreenWrapper {
  position: relative;
  background-image: url("/images/footer.webp");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: top;
  width: 100%;
  min-height: 1250px;
  display: flex;
  align-items: flex-end;
}
.footerContentWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.12);
  position: absolute;
  width: 98%;
  left: 50%;
  transform: translateX(-50%);
  color: #ffff;
  border-radius: 12px;
  flex-direction: column;
  bottom: 10px;
  padding-top: 2rem;
}
.footerContentWrapper div {
  width: 100%;
}
.footerContentRight {
  display: flex;
  flex-direction: column;
  border-left: none;
}
.footerContentRightTop {
  display: flex;
  width: 100% !important;
  text-align: left;
  border-bottom: 0.1px solid rgba(255, 255, 255, 0.3);
  border-top: 0.1px solid rgba(255, 255, 255, 0.3);
  margin-top: 2rem;
  flex-direction: column;
  padding: 1rem;
  box-sizing: border-box;
}
.footerContentRightTop div {
  width: 100%;
  margin-left: 0.3rem;
}
.footerContentRightTop ul {
  list-style: none;
  text-align: left;
  padding: 0;
  margin: 0;
  font-size: 1.2rem;
  font-family: var(--font-poppins);
}
.footerContentRightTop li {
  margin: 0.5rem 0;
  color: rgba(255, 255, 255, 0.7);
}
.linkHoverable:hover {
  display: inline;
  margin: 0.5rem 0;
  color: rgba(255, 255, 255, 1);
  border-bottom: 1px solid rgba(255, 255, 255, 1);
  box-sizing: border-box;
}
.storeIconsWrapper {
  display: grid;
  gap: 16px; /* Adjust spacing between items */
  grid-template-columns: repeat(4, 1fr);
  
      align-self: center;
    place-self: center;
    justify-self: center;
}

.footerIconsDiv {
  width: 100% !important;
  display: flex;
  justify-content: center;
  padding: 2rem 0 1rem 0;
  position: absolute;
  bottom: 350px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
}

.footerMainContent {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.footerContentLeft {
  display: flex;
  flex-direction: column;
  gap: 13px;
  padding: 1rem;
  box-sizing: border-box;
  padding: 0;
}
.footerContentLeft div {
  width: 80%;
  margin-left: 0.5rem;
}
.footerContentLeftImg {
  display: flex;
  gap: 5px;
}
.storeIcons {
  cursor: pointer;
}

@media (min-width: 768px) {
  .lockContentWrapper {
    font-size: 5rem;
  }
  .carouselBannerBtn {
    font-size: 2rem;
    padding: 0.8rem 3rem;
  }
  .footerGreenWrapper {
    min-height: 1050px;
    /* min-height: clamp(450px, 100vw + 450px, 1050px); */
  }
  .footerContentWrapper {
    flex-direction: column;
  }

  .footerMainContent {
    flex-direction: row;
    width: 100%;
  }
  .footerContentRight {
    border-left: 0.1px solid rgba(255, 255, 255, 0.3);
  }
  .footerContentRightTop {
    border-top: none;
    margin-top: 0;
    flex-direction: row;
    padding: 0;
  }
  .footerContentRightTop div {
    width: 33.3%;
    margin-left: 1rem;
  }
  .footerContentLeft {
    gap: 90px;
    padding-left: 2rem;
  }
  .footerContentRightTop div:nth-child(1),
  .footerContentRightTop div:nth-child(2) {
    border-right: 0.1px solid rgba(255, 255, 255, 0.3);
  }
  .footerMainContent div:nth-child(1) {
    width: 40%;
  }
  .gameCardCol2 div,
  .gameCardCol1 div {
    padding: 3px;
  }
  .gameCardsFirstSection p {
    font-size: 1rem;
  }

  .footerIconsDiv {
    bottom: 280px;
    transform: translateX(-50%);
  }

  .footerContentWrapper {
    padding-top: 2rem;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .storeIcons {
    width: 140px;
    height: 50px;
  }
  .footerGreenWrapper {
    min-height: 830px !important;
  }
  .footerContentRightTop div:nth-child(1),
  .footerContentRightTop div:nth-child(2) {
    width: 23%;
  }

  .footerIconsDiv {
    bottom: 220px;
    transform: translateX(-50%);
  }
}

@media (min-width: 1550px) {
  .footerGreenWrapper {
    min-height: 1200px !important;
  }
}

/* iPad Mini and small tablets */
@media (max-width: 820px) and (min-width: 701px) {
  .footerGreenWrapper {
    min-height: 900px !important;
  }
  .footerIconsDiv {
    bottom: 350px;
    transform: translateX(-50%);
  }
}

@media (max-width: 700px) {
  .footerGreenWrapper {
    min-height: 1100px !important;
  }
  .storeIconsWrapper {
    display: grid;
    gap: 16px;
    grid-template-columns: repeat(2, 1fr);
  }
  .storeIcons {
    width: 178px;
    height: 50px;
  }

  .footerIconsDiv {
    bottom: 450px;
    transform: translateX(-50%);
  }

  .footerContentWrapper {
    padding-top: 2rem;
  }
}

/* Very small mobile devices */
@media (max-width: 480px) {
  .footerGreenWrapper {
    min-height: 1000px !important;
  }
  .footerIconsDiv {
    bottom: 400px;
    transform: translateX(-50%);
  }
}




@media screen and (max-height: 500px) and (orientation: landscape) {
  .specificPageFooter {
    display: none !important;
  }
}


