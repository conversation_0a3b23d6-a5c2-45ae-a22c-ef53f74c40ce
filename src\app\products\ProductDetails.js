"use client";
import React, { useState, useEffect } from "react";
import styles from "./styles.module.css";
import Image from "next/image";
import dynamic from "next/dynamic";
import Carousel from "../../components/CarouselProduct";
import { useSearchParams } from "next/navigation";
import { gamesData, themeFilters } from "@/constants";
const ModalFullScreen = dynamic(() => import("@/components/ModalFullScreen"));
import useScrollAnimation from "@/hooks/useScrollAnimation";

const ProductsDetails = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState("All");
  const [modalData, setModalData] = useState([]);
  const searchParams = useSearchParams();

  const toggleModal = (gameData) => {
    setIsModalOpen((prev) => !prev);
    setModalData(gameData);
  };

  const handleFilterClick = (filter) => {
    setSelectedFilter(filter);
  };

  const filteredGames =
    selectedFilter === "All"
      ? gamesData
      : gamesData.filter((game) => game.interests.includes(selectedFilter.toUpperCase()));

  useEffect(() => {
    if (searchParams.get("theme")) {
      const section = document.getElementById("target-section");
      handleFilterClick(searchParams.get("theme"));
      section.scrollIntoView({ behavior: "smooth" });
    }
  }, []);

  const setRef = useScrollAnimation(
    {
      duration: 1,
      ease: "power3.out",
      start: "top bottom",
    },
    []
  );

  return (
    <>
      <main className={styles.productsPageWrapper}>
        <Carousel />
        <div className={styles.productFilterGamesWrapper}>
          <div
            id="target-section"
            className={styles.filterParameterWrapper}
            // ref={(el) => setRef(el)}
          >
            {themeFilters.map((filter, index) => (
              <p
                key={index}
                onClick={() => handleFilterClick(filter)}
                className={selectedFilter === filter ? styles.activeFilter : ""}
              >
                {filter}
              </p>
            ))}
          </div>
          <section className={styles.gameCardsWrapper}>
            {filteredGames.map((game, index) => {
              return (
                <div
                  key={game.gameName}
                  onClick={() => toggleModal(game)}
                  className={styles.productCards}
                  // ref={(el) => setRef(el)}
                >
                  <Image
                    src={game.gameThumbnailImg}
                    alt={game.gameName}
                    className={styles.gameCardsImg}
                  />
                  <p className={styles.gameName}>{game.gameName}</p>
                  <p className={styles.gameDescription}>{game.gameDesc}</p>
                  <div className={styles.gameSkillsWrapper}>
                    <p>{game.gameSkill}</p>
                  </div>
                  <button className={styles.gameCardBtn}>Learn More</button>
                </div>
              );
            })}
          </section>
        </div>
        {isModalOpen && (
          <ModalFullScreen toggleModal={toggleModal} modalData={modalData} isOpen={isModalOpen} />
        )}
      </main>
    </>
  );
};

export default ProductsDetails;
